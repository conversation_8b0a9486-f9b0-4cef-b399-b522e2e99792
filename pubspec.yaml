name: towchain_service_provider
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.7
  go_router: ^15.1.2
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5  # Align with hooks_riverpod
  freezed_annotation: ^2.4.4  # Corrected version
  toastification: ^3.0.3
  flutter_smart_dialog: ^4.9.8+8
  another_flushbar: ^1.12.30
  cached_network_image: ^3.3.0
  google_fonts: ^6.2.1
  flutter_launcher_icons: ^0.14.3
  flutter_remix: ^0.0.3
  country_picker: ^2.0.27 # or the latest version
  geolocator: ^12.0.0 # or the latest version
  geocoding: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/

  #- .env
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Urbanist-Medium.ttf
        - asset: assets/fonts/Urbanist-Regular.ttf
        - asset: assets/fonts/Urbanist-SemiBold.ttf
        - asset: assets/fonts/Urbanist-Bold.ttf

flutter_launcher_icons:
  android: false
  ios: true
  image_path: "assets/images/Applogo.png"
