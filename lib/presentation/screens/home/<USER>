import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:towchain_service_provider/core/widgets/CustomAppBar.dart';
import 'package:towchain_service_provider/core/widgets/custom_text.dart';
import '../../../../core/assets_manager.dart';
import '../../../../core/services/user_storage_service.dart';
import '../../../core/imports/core_imports.dart';
import 'widgets/status_widgets.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  late GoogleMapController mapController;
  final LatLng _center = const LatLng(
    30.7046,
    76.7179,
  ); // Use a single, consistent location
  final Set<Marker> _markers = {};
  BitmapDescriptor? customMarkerIcon;

  // Status variables
  bool _isOnline = false;
  bool _isLicenseExpired = false;
  String _userName = 'User';
  String _userEmail = '';

  @override
  void initState() {
    super.initState();
    _loadCustomMarkerIcon();
    _loadUserData();
    _loadStatusData();
  }

  Future<void> _loadCustomMarkerIcon() async {
    // customMarkerIcon = await BitmapDescriptor.fromAssetImage(
    //   const ImageConfiguration(size: Size(48, 48)),
    //   'assets/setlocation.png',
    // );
    // No need to call _addMarkers() here.
    // We'll call it in _onMapCreated instead.
  }

  void _onMapCreated(GoogleMapController controller) {
    mapController = controller;

    // Animate camera and add markers only after the map controller is ready
    mapController
        .animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: _center, // Use the correct center
              zoom: 12.0,
            ),
          ),
        )
        .then((_) {
          _addMarkers(); // Call _addMarkers after the camera animation is complete
        });
  }

  void _addMarkers() {
    if (customMarkerIcon != null) {
      setState(() {
        _markers.add(
          Marker(
            markerId: const MarkerId('current_location'),
            position: _center,
            infoWindow: const InfoWindow(
              title: 'Sahibzada Ajit Singh Nagar',
              snippet: 'My Custom Location',
            ),
            icon: customMarkerIcon!,
          ),
        );
        _markers.add(
          Marker(
            markerId: const MarkerId('another_place'),
            position: const LatLng(30.7333, 76.7794),
            infoWindow: const InfoWindow(
              title: 'Nearby City',
              snippet: 'Another custom marker',
            ),
            icon: customMarkerIcon!,
          ),
        );
      });
    }
  }

  Future<void> _loadUserData() async {
    final userData = await UserStorageService.getUserData();
    if (userData != null) {
      setState(() {
        _userName = userData['name'] ?? 'User';
        _userEmail = userData['email'] ?? '';
      });
    }
  }

  Future<void> _loadStatusData() async {
    final isOnline = await UserStorageService.getOnlineStatus();
    final isLicenseExpired =await UserStorageService.isLicenseExpired();

    setState(() {
      _isOnline = isOnline;
      _isLicenseExpired = isLicenseExpired;
    });
  }

  void _toggleOnlineStatus(bool value) async {
    await UserStorageService.setOnlineStatus(value);
    setState(() {
      _isOnline = value;
    });
  }

  void _navigateToLicenseScreen() {
    context.push('/license');
  }

  // @override
  // Widget build(BuildContext context) {
  //   final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
  //   return Scaffold(
  //     key: scaffoldKey,
  //     drawer: Drawer(
  //       // ... your drawer code remains the same ...
  //       child: SingleChildScrollView(
  //         child: Column(
  //           children: [
  //             Container(
  //               height: 230,
  //               width: double.infinity,
  //               decoration: const BoxDecoration(
  //                 gradient: LinearGradient(
  //                   begin: Alignment.topCenter,
  //                   end: Alignment.bottomCenter,
  //                   colors: [Color(0xFF41D5FB), Color(0xFF01AEFF)],
  //                 ),
  //               ),
  //               child: Padding(
  //                 padding: const EdgeInsets.symmetric(
  //                   horizontal: 20.0,
  //                   vertical: 20.0,
  //                 ),
  //                 child: Column(
  //                   mainAxisAlignment: MainAxisAlignment.end,
  //                   crossAxisAlignment: CrossAxisAlignment.start,
  //                   children: [
  //                     Align(
  //                       alignment: Alignment.centerLeft,
  //                       child: Stack(
  //                         alignment: Alignment.center,
  //                         children: [
  //                           CircleAvatar(
  //                             radius: 50,
  //                             backgroundColor: Colors.white,
  //                             child: CircleAvatar(
  //                               radius: 58,
  //                               backgroundImage: AssetImage(
  //                                 'assets/images/user.png',
  //                               ),
  //                             ),
  //                           ),
  //                           Positioned(
  //                             top: 0,
  //                             right: 0,
  //                             child: CircleAvatar(
  //                               radius: 18,
  //                               backgroundColor: Colors.white,
  //                               child: IconButton(
  //                                 icon: Image.asset(
  //                                   AssetsManager.on_board_one,
  //                                   width: 24,
  //                                   height: 24,
  //                                 ),
  //                                 onPressed: () {},
  //                               ),
  //                             ),
  //                           ),
  //                         ],
  //                       ),
  //                     ),
  //                     const SizedBox(height: 10.0),
  //                     const Text(
  //                       'Anna Iannings',
  //                       style: TextStyle(
  //                         fontSize: 20,
  //                         fontWeight: FontWeight.w600,
  //                         color: Colors.white,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 4.0),
  //                     Row(
  //                       children: [
  //                         Image.asset(
  //                           AssetsManager.on_board_one,
  //                           width: 16,
  //                           height: 16,
  //                         ),
  //                         const SizedBox(width: 8.0),
  //                         const Text(
  //                           '<EMAIL>',
  //                           style: TextStyle(
  //                             fontSize: 16,
  //                             fontWeight: FontWeight.w500,
  //                             color: Colors.white,
  //                           ),
  //                         ),
  //                       ],
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //             ListTile(
  //               contentPadding: EdgeInsets.only(left: 18, right: 18, top: 10),
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'Home',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.texGreyColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.sideMenu,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //             ListTile(
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'Call History',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.blackColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //             ListTile(
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'Orders History',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.blackColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //             ListTile(
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'Setting',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.blackColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //             ListTile(
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'How it Work',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.blackColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //             ListTile(
  //               leading: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 20,
  //                 height: 20,
  //               ),
  //               title: const CustomText(
  //                 text: 'Help',
  //                 weight: FontWeight.w400,
  //                 size: 14,
  //                 color: AppColors.blackColor,
  //               ),
  //               trailing: Image.asset(
  //                 AssetsManager.on_board_one,
  //                 width: 16,
  //                 height: 16,
  //               ),
  //               onTap: () {
  //                 if (Navigator.of(context).canPop()) {
  //                   Navigator.pop(context);
  //                 }
  //               },
  //             ),
  //             Divider(height: 2, color: AppColors.blackColor),
  //           ],
  //         ),
  //       ),
  //     ),
  //     appBar: CustomAppBar(
  //       title: _isOnline ? 'Online' : 'Offline',
  //       leading: Center(
  //         child: Padding(
  //           padding: const EdgeInsets.only(left: 10.0),
  //           child: IconButton(
  //             alignment: Alignment.topLeft,
  //             icon: Image.asset(
  //               alignment: Alignment.center,
  //               AssetsManager.sideMenu,
  //               height: 34,
  //               width: 34,
  //             ),
  //             onPressed: () {
  //               scaffoldKey.currentState!.openDrawer();
  //             },
  //           ),
  //         ),
  //       ),
  //       actions: [
  //         Padding(
  //           padding: const EdgeInsets.only(right: 16.0),
  //           child: SizedBox(
  //             width: 60,
  //             height: 45,
  //             child: FittedBox(
  //               fit: BoxFit.fill,
  //               child: Switch(
  //                 value: _isOnline,
  //                 onChanged: _toggleOnlineStatus,
  //                 activeThumbColor: AppColors.whiteColor,
  //                 activeTrackColor: AppColors.primaryColor,
  //                 inactiveThumbColor: Colors.black,
  //                 inactiveTrackColor: AppColors.textGreyColor,
  //               ),
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //     body: Stack(
  //       children: [
  //         // Google Map (full screen)
  //         GoogleMap(
  //           onMapCreated: _onMapCreated,
  //           initialCameraPosition: CameraPosition(
  //             target: _center, // Use the correct center
  //             zoom: 11.0,
  //           ),
  //           markers: _markers,
  //         ),
  //         Positioned(
  //           right: 15.0,
  //           top: MediaQuery.of(context).size.height * 0.25,
  //           child: Padding(
  //             padding: const EdgeInsets.all(8.0),
  //             child: Container(
  //               decoration: BoxDecoration(
  //                 borderRadius: BorderRadius.circular(30.0),
  //                 border: Border.all(
  //                   color: AppColors.lightGreyColor,
  //                   width: 1.0,
  //                 ),
  //               ),
  //               child: Padding(
  //                 padding: const EdgeInsets.all(3.0),
  //                 child: Column(
  //                   children: [
  //                     FloatingActionButton(
  //                       heroTag: "fab1",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.transparent,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 40,
  //                         width: 40,
  //                         AssetsManager.yellowTow,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 5),
  //                     FloatingActionButton(
  //                       heroTag: "fab2",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.white,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 40,
  //                         width: 40,
  //                         AssetsManager.mapNearby,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 5),
  //                     FloatingActionButton(
  //                       heroTag: "fab3",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.white,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 40,
  //                         width: 40,
  //                         AssetsManager.navigate,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 5),
  //                     FloatingActionButton(
  //                       heroTag: "fab4",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.white,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 20,
  //                         width: 20,
  //                         AssetsManager.mapMini,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 5),
  //                     FloatingActionButton(
  //                       heroTag: "fab5",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.white,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 25,
  //                         width: 25,
  //                         AssetsManager.plus,
  //                       ),
  //                     ),
  //                     const SizedBox(height: 5),
  //                     FloatingActionButton(
  //                       heroTag: "fab6",
  //                       mini: true,
  //                       onPressed: () {},
  //                       backgroundColor: Colors.white,
  //                       shape: const CircleBorder(),
  //                       child: Image.asset(
  //                         height: 25,
  //                         width: 25,
  //                         AssetsManager.minus,
  //                       ),
  //                     ),
  //                   ],
  //                 ),
  //               ),
  //             ),
  //           ),
  //         ),
  //       ],
  //     ),
  //   );
  // }

  @override
  Widget build(BuildContext context) {
    final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey<ScaffoldState>();
    return Scaffold(
      key: scaffoldKey,
      drawer: Drawer(
        // ... your drawer code remains the same ...
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                height: 230,
                width: double.infinity,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [Color(0xFF41D5FB), Color(0xFF01AEFF)],
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20.0,
                    vertical: 20.0,
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            CircleAvatar(
                              radius: 50,
                              backgroundColor: Colors.white,
                              child: CircleAvatar(
                                radius: 58,
                                backgroundImage: AssetImage(
                                  'assets/images/user.png',
                                ),
                              ),
                            ),
                            Positioned(
                              top: 0,
                              right: 0,
                              child: CircleAvatar(
                                radius: 18,
                                backgroundColor: Colors.white,
                                child: IconButton(
                                  icon: Image.asset(
                                    AssetsManager.on_board_one,
                                    width: 24,
                                    height: 24,
                                  ),
                                  onPressed: () {},
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 10.0),
                      const Text(
                        'Anna Iannings',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 4.0),
                      Row(
                        children: [
                          Image.asset(
                            AssetsManager.on_board_one,
                            width: 16,
                            height: 16,
                          ),
                          const SizedBox(width: 8.0),
                          const Text(
                            '<EMAIL>',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              ListTile(
                contentPadding: EdgeInsets.only(left: 18, right: 18, top: 10),
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'Home',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.texGreyColor,
                ),
                trailing: Image.asset(
                  AssetsManager.sideMenu,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
              ListTile(
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'Call History',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.blackColor,
                ),
                trailing: Image.asset(
                  AssetsManager.on_board_one,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
              ListTile(
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'Orders History',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.blackColor,
                ),
                trailing: Image.asset(
                  AssetsManager.on_board_one,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
              ListTile(
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'Setting',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.blackColor,
                ),
                trailing: Image.asset(
                  AssetsManager.on_board_one,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
              ListTile(
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'How it Work',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.blackColor,
                ),
                trailing: Image.asset(
                  AssetsManager.on_board_one,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
              ListTile(
                leading: Image.asset(
                  AssetsManager.on_board_one,
                  width: 20,
                  height: 20,
                ),
                title: const CustomText(
                  text: 'Help',
                  weight: FontWeight.w400,
                  size: 14,
                  color: AppColors.blackColor,
                ),
                trailing: Image.asset(
                  AssetsManager.on_board_one,
                  width: 16,
                  height: 16,
                ),
                onTap: () {
                  if (Navigator.of(context).canPop()) {
                    Navigator.pop(context);
                  }
                },
              ),
              Divider(height: 2, color: AppColors.blackColor),
            ],
          ),
        ),
      ),
      appBar: CustomAppBar(
        title: _isOnline ? 'Online' : 'Offline',
        leading: Center(
          child: Padding(
            padding: const EdgeInsets.only(left: 10.0),
            child: IconButton(
              alignment: Alignment.topLeft,
              icon: Image.asset(
                alignment: Alignment.center,
                AssetsManager.sideMenu,
                height: 34,
                width: 34,
              ),
              onPressed: () {
                scaffoldKey.currentState!.openDrawer();
              },
            ),
          ),
        ),
        actions: [
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: SizedBox(
              width: 60,
              height: 45,
              child: FittedBox(
                fit: BoxFit.fill,
                child: Switch(
                  value: _isOnline,
                  onChanged: _toggleOnlineStatus,
                  activeThumbColor: AppColors.whiteColor,
                  activeTrackColor: AppColors.primaryColor,
                  inactiveThumbColor: Colors.black,
                  inactiveTrackColor: AppColors.textGreyColor,
                ),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Status widgets container - only show when needed
          Container(
            padding: const EdgeInsets.all(0),
            child: Column(
              children: [
                // Online/Offline status widget - only show when offline
                OnlineStatusWidget(isOnline: _isOnline),

                // License expired warning - only show when expired
                if (_isLicenseExpired == true) ...[
                  LicenseExpiredWidget(
                    onRenewPressed: _navigateToLicenseScreen,
                  ),
                ],
              ],
            ),
          ),
          // Map View (wrapped in Expanded)
          Expanded(
            child: Stack(
              children: [
                GoogleMap(
                  onMapCreated: _onMapCreated,
                  initialCameraPosition: CameraPosition(
                    target: _center, // Use the correct center
                    zoom: 11.0,
                  ),
                  markers: _markers,
                ),
                Positioned(
                  right: 15.0,
                  top: 10,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30.0),
                        border: Border.all(
                          color: AppColors.lightGreyColor,
                          width: 1.0,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(3.0),
                        child: Column(
                          children: [
                            FloatingActionButton(
                              heroTag: "fab1",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.transparent,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 40,
                                width: 40,
                                AssetsManager.yellowTow,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab2",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 40,
                                width: 40,
                                AssetsManager.mapNearby,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab3",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 40,
                                width: 40,
                                AssetsManager.navigate,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab4",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 20,
                                width: 20,
                                AssetsManager.mapMini,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab5",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 25,
                                width: 25,
                                AssetsManager.plus,
                              ),
                            ),
                            const SizedBox(height: 5),
                            FloatingActionButton(
                              heroTag: "fab6",
                              mini: true,
                              onPressed: () {},
                              backgroundColor: Colors.white,
                              shape: const CircleBorder(),
                              child: Image.asset(
                                height: 25,
                                width: 25,
                                AssetsManager.minus,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
