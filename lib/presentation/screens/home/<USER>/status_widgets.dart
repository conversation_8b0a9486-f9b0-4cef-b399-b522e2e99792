import 'package:flutter/material.dart';
import '../../../../core/theme/colors.dart';
import '../../../../core/widgets/custom_text.dart';

class OnlineStatusWidget extends StatelessWidget {
  final bool isOnline;
  
  const OnlineStatusWidget({
    super.key,
    required this.isOnline,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isOnline ? Colors.green : Colors.orange,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              isOnline ? Icons.check : Icons.wifi_off,
              color: isOnline ? Colors.green : Colors.orange,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: CustomText(
              text: isOnline ? "You are Online!" : "You are Offline!",
              color: Colors.white,
              size: 16,
              weight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class LicenseExpiredWidget extends StatelessWidget {
  final VoidCallback onRenewPressed;
  
  const LicenseExpiredWidget({
    super.key,
    required this.onRenewPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.warning,
              color: Colors.red,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomText(
                  text: "Driving License Expired",
                  color: Colors.white,
                  size: 14,
                  weight: FontWeight.w600,
                ),
                const SizedBox(height: 2),
                CustomText(
                  text: "You need to renew your license to continue",
                  color: Colors.white.withValues(alpha: 0.9),
                  size: 12,
                  weight: FontWeight.w400,
                ),
              ],
            ),
          ),
          const SizedBox(width: 8),
          GestureDetector(
            onTap: onRenewPressed,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
              ),
              child: CustomText(
                text: "Renew",
                color: Colors.red,
                size: 12,
                weight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
