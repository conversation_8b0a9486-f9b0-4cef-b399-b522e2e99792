import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/colors.dart';
import '../../../core/widgets/custom_text.dart';
import '../../../core/widgets/custom_button.dart';
import '../../../core/services/user_storage_service.dart';

class LicenseScreen extends StatefulWidget {
  const LicenseScreen({super.key});

  @override
  State<LicenseScreen> createState() => _LicenseScreenState();
}

class _LicenseScreenState extends State<LicenseScreen> {
  bool _isLoading = false;

  Future<void> _renewLicense() async {
    setState(() {
      _isLoading = true;
    });

    // Simulate license renewal process
    await Future.delayed(const Duration(seconds: 2));

    // Update license status
    await UserStorageService.setLicenseExpired(false);

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('License renewed successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Go back to home
      context.pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const CustomText(
          text: 'License Renewal',
          size: 18,
          weight: FontWeight.w600,
          color: AppColors.blackColor,
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppColors.blackColor),
          onPressed: () => context.pop(),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 20),
            
            // Warning icon
            Center(
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.warning,
                  color: Colors.red,
                  size: 40,
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // Title
            const Center(
              child: CustomText(
                text: 'License Expired',
                size: 24,
                weight: FontWeight.w700,
                color: AppColors.blackColor,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Description
            const Center(
              child: CustomText(
                text: 'Your driving license has expired. You need to renew it to continue providing towing services.',
                size: 16,
                weight: FontWeight.w400,
                color: AppColors.greyColor,
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 40),
            
            // License details
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomText(
                    text: 'License Details',
                    size: 16,
                    weight: FontWeight.w600,
                    color: AppColors.blackColor,
                  ),
                  SizedBox(height: 8),
                  CustomText(
                    text: 'License Number: DL-**********',
                    size: 14,
                    weight: FontWeight.w400,
                    color: AppColors.greyColor,
                  ),
                  SizedBox(height: 4),
                  CustomText(
                    text: 'Expired Date: 15/08/2024',
                    size: 14,
                    weight: FontWeight.w400,
                    color: Colors.red,
                  ),
                ],
              ),
            ),
            
            const Spacer(),
            
            // Renew button
            CustomButton(
              text: _isLoading ? 'Renewing...' : 'Renew License',
              onPressed: _isLoading ? null : _renewLicense,
              color: AppColors.primaryColor,
            ),
            
            const SizedBox(height: 16),
            
            // Skip button
            TextButton(
              onPressed: () => context.pop(),
              child: const CustomText(
                text: 'Skip for now',
                size: 16,
                weight: FontWeight.w500,
                color: AppColors.greyColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
