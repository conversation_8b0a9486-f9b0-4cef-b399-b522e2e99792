import 'package:go_router/go_router.dart';
import 'package:towchain_service_provider/main.dart';
import '../../../../core/imports/core_imports.dart';
import '../../../../core/widgets/custom_button.dart';
import '../model/slider_model.dart';

class EnableLocationScreen extends StatefulWidget {
  const EnableLocationScreen({super.key});

  @override
  _EnableLocationScreenState createState() => _EnableLocationScreenState();
}

class _EnableLocationScreenState extends State<EnableLocationScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [],
      ),
    )
  }
  
}
