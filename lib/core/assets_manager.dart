import 'dart:ui';

import 'package:towchain_service_provider/core/imports/packages_imports.dart';

class AssetsManager {
  static const String imagesCommon = 'assets/images';
  static const String iconsCommon = 'assets/icons';

  static const String on_board_one = '$imagesCommon/onboardone.png';
  static const String on_board_two = '$imagesCommon/onboardtwo.png';
  static const String on_board_three = '$imagesCommon/onboardthree.png';
  static const String splash = '$imagesCommon/splash.png';
  static const String logo_with_text = '$imagesCommon/logowithtext.png';
  static const String authbg = '$imagesCommon/authbg.png';
  static const String otp = '$imagesCommon/otp.png';
  static const String enable_location = '$imagesCommon/EnableLocation.png';
  static const String back = '$iconsCommon/back.svg';
}

class FontsManager {
  static const String urbanist = "Urbanist";
  static const String inter = "Inter"; // if you also use Inter
}

class UrbanistText {
  static TextStyle regular({double fontSize = 14, Color color = Colors.black}) {
    return TextStyle(
      fontFamily: 'Urbanist',
      fontWeight: FontWeight.w400,
      fontSize: fontSize,
      color: color,
    );
  }

  static TextStyle medium({double fontSize = 14, Color color = Colors.black}) {
    return TextStyle(
      fontFamily: 'Urbanist',
      fontWeight: FontWeight.w500,
      fontSize: fontSize,
      color: color,
    );
  }

  static TextStyle semiBold({
    double fontSize = 16,
    Color color = Colors.black,
  }) {
    return TextStyle(
      fontFamily: 'Urbanist',
      fontWeight: FontWeight.w600,
      fontSize: fontSize,
      color: color,
    );
  }

  static TextStyle bold({double fontSize = 18, Color color = Colors.black}) {
    return TextStyle(
      fontFamily: 'Urbanist',
      fontWeight: FontWeight.w700,
      fontSize: fontSize,
      color: color,
    );
  }
}
