import 'dart:ui';

import 'package:towchain_service_provider/core/imports/packages_imports.dart';

class AssetsManager {
  static const String imagesCommon = 'assets/images';
  static const String iconsCommon = 'assets/icons';

  static const String on_board_one = '$imagesCommon/onboardone.png';
  static const String on_board_two = '$imagesCommon/onboardtwo.png';
  static const String on_board_three = '$imagesCommon/onboardthree.png';
  static const String splash = '$imagesCommon/splash.png';
  static const String logo_with_text = '$imagesCommon/logowithtext.png';
  static const String authbg = '$imagesCommon/authbg.png';
  static const String otp = '$imagesCommon/otp.png';
  static const String enable_location = '$imagesCommon/EnableLocation.png';
  static const String back = '$iconsCommon/back.svg';
  static const String navigation = '$imagesCommon/navigation.png';
  static const String plus = '$imagesCommon/plus.png';
  static const String yellowTow = '$imagesCommon/yellowTow.png';
  static const String minus = '$imagesCommon/minus.png';
  static const String mapMini = '$imagesCommon/mapmini.png';
  static const String mapNearby = '$imagesCommon/mapNearby.png';
  static const String sideMenu = '$imagesCommon/sideMenu.png';
  static const String navigate = '$imagesCommon/navigate.png';
}

class FontsManager {
  static const String urbanist = "Urbanist";
  static const String inter = "Inter"; // if you also use Inter
}
