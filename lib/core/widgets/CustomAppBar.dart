import 'package:towchain_service_provider/core/widgets/custom_text.dart';
import '../imports/core_imports.dart';

class CustomAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final Widget? leading;
  final List<Widget>? actions;

  const CustomAppBar({
    super.key,
    required this.title,
    this.leading,
    this.actions,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: CustomText(
        text: title,
        fontFamily: 'Urbanist-SemiBold',
        size: 18,
        weight: FontWeight.w600,
        color: AppColors.whiteColor,
      ),
      leading: leading,
      actions: actions,
      backgroundColor: Colors.black, // Dark background color
      elevation: 0, // No shadow
    );
  }
}
